<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link
      rel="icon"
      href="resources/favicon.ico"
      type="image/x-icon"
      sizes="96x96"
    />

    <title>Random Quote | devChallenges.io</title>

    <style>
      @import url('https://fonts.googleapis.com/css2?family=Raleway:wght@400;500;700&display=swap');

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Raleway', sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;
        position: relative;
      }

      .container {
        max-width: 600px;
        width: 100%;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 60px 40px;
        text-align: center;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
        position: relative;
      }

      .quote-icon {
        font-size: 48px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 30px;
      }

      .quote-text {
        font-size: 36px;
        font-weight: 500;
        color: white;
        line-height: 1.4;
        margin-bottom: 40px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .author-section {
        margin-bottom: 50px;
      }

      .author-name {
        font-size: 24px;
        font-weight: 700;
        color: white;
        margin-bottom: 8px;
      }

      .author-tags {
        display: flex;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
      }

      .tag {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .buttons {
        display: flex;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap;
      }

      .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        font-family: 'Raleway', sans-serif;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .btn-random {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .btn-random:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }

      .btn-share {
        background: rgba(255, 255, 255, 0.9);
        color: #667eea;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .btn-share:hover {
        background: white;
        transform: translateY(-2px);
      }

      .author-info {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        text-align: center;
        color: rgba(255, 255, 255, 0.7);
      }

      .author-info a {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
      }

      .author-info a:hover {
        text-decoration: underline;
      }

      @media (max-width: 768px) {
        .container {
          padding: 40px 30px;
        }

        .quote-text {
          font-size: 28px;
        }

        .author-name {
          font-size: 20px;
        }

        .buttons {
          flex-direction: column;
          align-items: center;
        }

        .btn {
          width: 200px;
          justify-content: center;
        }
      }

      @media (max-width: 480px) {
        .container {
          padding: 30px 20px;
        }

        .quote-text {
          font-size: 24px;
        }

        .author-tags {
          flex-direction: column;
          align-items: center;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="quote-icon">"</div>

      <div class="quote-text" id="quote-text">
        "Learn from yesterday, live for today, hope for tomorrow."
      </div>

      <div class="author-section">
        <div class="author-name" id="author-name">George Bernard Shaw</div>
        <div class="author-tags">
          <span class="tag">Famous Quotes</span>
          <span class="tag">Inspirational</span>
        </div>
      </div>

      <div class="buttons">
        <button class="btn btn-random" id="random-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
          </svg>
          Random
        </button>
        <button class="btn btn-share" id="share-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M18,16.08C17.24,16.08 16.56,16.38 16.04,16.85L8.91,12.7C8.96,12.47 9,12.24 9,12C9,11.76 8.96,11.53 8.91,11.3L15.96,7.19C16.5,7.69 17.21,8 18,8A3,3 0 0,0 21,5A3,3 0 0,0 18,2A3,3 0 0,0 15,5C15,5.24 15.04,5.47 15.09,5.7L8.04,9.81C7.5,9.31 6.79,9 6,9A3,3 0 0,0 3,12A3,3 0 0,0 6,15C6.79,15 7.5,14.69 8.04,14.19L15.16,18.34C15.11,18.55 15.08,18.77 15.08,19C15.08,20.61 16.39,21.91 18,21.91C19.61,21.91 20.92,20.61 20.92,19A2.92,2.92 0 0,0 18,16.08Z" />
          </svg>
          Share
        </button>
      </div>
    </div>

    <div class="author-info">
      Coded by <a href="#">Ayokanmi Adejola</a> | Challenge by
      <a href="https://www.devchallenges.io?ref=challenge" target="_blank"
        >devChallenges.io</a
      >.
    </div>

    <script>
      const quotes = [
        {
          text: "Learn from yesterday, live for today, hope for tomorrow.",
          author: "George Bernard Shaw",
          tags: ["Famous Quotes", "Inspirational"]
        },
        {
          text: "The only way to do great work is to love what you do.",
          author: "Steve Jobs",
          tags: ["Motivational", "Work"]
        },
        {
          text: "Life is what happens to you while you're busy making other plans.",
          author: "John Lennon",
          tags: ["Life", "Philosophy"]
        },
        {
          text: "The future belongs to those who believe in the beauty of their dreams.",
          author: "Eleanor Roosevelt",
          tags: ["Dreams", "Future"]
        },
        {
          text: "It is during our darkest moments that we must focus to see the light.",
          author: "Aristotle",
          tags: ["Hope", "Wisdom"]
        },
        {
          text: "The only impossible journey is the one you never begin.",
          author: "Tony Robbins",
          tags: ["Journey", "Motivation"]
        },
        {
          text: "In the end, we will remember not the words of our enemies, but the silence of our friends.",
          author: "Martin Luther King Jr.",
          tags: ["Friendship", "Courage"]
        },
        {
          text: "Success is not final, failure is not fatal: it is the courage to continue that counts.",
          author: "Winston Churchill",
          tags: ["Success", "Courage"]
        },
        {
          text: "The way to get started is to quit talking and begin doing.",
          author: "Walt Disney",
          tags: ["Action", "Success"]
        },
        {
          text: "Don't let yesterday take up too much of today.",
          author: "Will Rogers",
          tags: ["Present", "Wisdom"]
        }
      ];

      let currentQuoteIndex = 0;

      function displayQuote(quote) {
        document.getElementById('quote-text').textContent = `"${quote.text}"`;
        document.getElementById('author-name').textContent = quote.author;

        const tagsContainer = document.querySelector('.author-tags');
        tagsContainer.innerHTML = '';
        quote.tags.forEach(tag => {
          const tagElement = document.createElement('span');
          tagElement.className = 'tag';
          tagElement.textContent = tag;
          tagsContainer.appendChild(tagElement);
        });
      }

      function getRandomQuote() {
        let randomIndex;
        do {
          randomIndex = Math.floor(Math.random() * quotes.length);
        } while (randomIndex === currentQuoteIndex && quotes.length > 1);

        currentQuoteIndex = randomIndex;
        return quotes[randomIndex];
      }

      function shareQuote() {
        const currentQuote = quotes[currentQuoteIndex];
        const shareText = `"${currentQuote.text}" - ${currentQuote.author}`;

        if (navigator.share) {
          navigator.share({
            title: 'Inspirational Quote',
            text: shareText,
            url: window.location.href
          });
        } else {
          // Fallback for browsers that don't support Web Share API
          navigator.clipboard.writeText(shareText).then(() => {
            alert('Quote copied to clipboard!');
          }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = shareText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('Quote copied to clipboard!');
          });
        }
      }

      // Event listeners
      document.getElementById('random-btn').addEventListener('click', () => {
        const newQuote = getRandomQuote();
        displayQuote(newQuote);
      });

      document.getElementById('share-btn').addEventListener('click', shareQuote);

      // Initialize with first quote
      displayQuote(quotes[currentQuoteIndex]);
    </script>
  </body>
</html>
