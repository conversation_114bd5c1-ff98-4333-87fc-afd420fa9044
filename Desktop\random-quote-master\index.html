<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link
      rel="icon"
      href="resources/favicon.ico"
      type="image/x-icon"
      sizes="96x96"
    />

    <title>Random Quote | devChallenges.io</title>

    <style>
      @import url('https://fonts.googleapis.com/css2?family=Raleway:wght@400;500;700&display=swap');

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Raleway', sans-serif;
        background-color: #20293A;
        background-image: url('resources/bg-image-random-quote.svg');
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;
        position: relative;
      }

      .container {
        max-width: 544px;
        width: 100%;
        background: #20293A;
        border-radius: 12px;
        padding: 48px 40px;
        text-align: center;
        position: relative;
      }

      .quote-text {
        font-size: 36px;
        font-weight: 500;
        color: #F7DF94;
        line-height: 1.2;
        margin-bottom: 32px;
        font-family: 'Raleway', sans-serif;
      }

      .author-section {
        margin-bottom: 48px;
      }

      .author-name {
        font-size: 24px;
        font-weight: 700;
        color: #FFFFFF;
        margin-bottom: 16px;
        font-family: 'Raleway', sans-serif;
      }

      .author-tags {
        display: flex;
        justify-content: center;
        gap: 16px;
        flex-wrap: wrap;
      }

      .tag {
        background: #4A5567;
        color: #FFFFFF;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        font-family: 'Raleway', sans-serif;
      }

      .buttons {
        display: flex;
        justify-content: center;
        gap: 24px;
        flex-wrap: wrap;
      }

      .btn {
        padding: 16px 24px;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        font-family: 'Raleway', sans-serif;
        display: flex;
        align-items: center;
        gap: 12px;
        min-width: 120px;
        justify-content: center;
      }

      .btn-random {
        background: #4A5567;
        color: #FFFFFF;
      }

      .btn-random:hover {
        background: #5A6577;
      }

      .btn-share {
        background: #4A5567;
        color: #FFFFFF;
      }

      .btn-share:hover {
        background: #5A6577;
      }

      .btn svg {
        width: 20px;
        height: 20px;
        fill: currentColor;
      }

      .author-info {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        text-align: center;
        color: #BDBDBD;
        font-family: 'Raleway', sans-serif;
      }

      .author-info a {
        color: #BDBDBD;
        text-decoration: none;
      }

      .author-info a:hover {
        text-decoration: underline;
      }

      @media (max-width: 768px) {
        .container {
          padding: 40px 30px;
          max-width: 90%;
        }

        .quote-text {
          font-size: 28px;
        }

        .author-name {
          font-size: 20px;
        }

        .buttons {
          flex-direction: column;
          align-items: center;
        }

        .btn {
          width: 200px;
        }
      }

      @media (max-width: 480px) {
        .container {
          padding: 30px 20px;
        }

        .quote-text {
          font-size: 24px;
        }

        .author-tags {
          flex-direction: column;
          align-items: center;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="quote-text" id="quote-text">
        "Learn from yesterday, live for today, hope for tomorrow."
      </div>

      <div class="author-section">
        <div class="author-name" id="author-name">George Bernard Shaw</div>
        <div class="author-tags">
          <span class="tag">Famous Quotes</span>
          <span class="tag">Inspirational</span>
        </div>
      </div>

      <div class="buttons">
        <button class="btn btn-random" id="random-btn">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 15.2L2.29289 14.4928L1.58579 15.2L2.29289 15.9071L3 15.2ZM9.8 15.2L9.8 16.2L9.8 15.2ZM14.8 11.2C14.8 10.6477 14.3523 10.2 13.8 10.2C13.2477 10.2 12.8 10.6477 12.8 11.2L14.8 11.2ZM6.29289 10.4928L2.29289 14.4928L3.70711 15.9071L7.70711 11.9071L6.29289 10.4928ZM2.29289 15.9071L6.29289 19.9071L7.70711 18.4928L3.70711 14.4928L2.29289 15.9071ZM3 16.2L9.8 16.2L9.8 14.2L3 14.2L3 16.2ZM9.8 16.2C12.5614 16.2 14.8 13.9614 14.8 11.2L12.8 11.2C12.8 12.8568 11.4569 14.2 9.8 14.2L9.8 16.2Z" fill="currentColor"/>
            <path d="M21.5 8.80004L22.2071 9.50714L22.9142 8.80004L22.2071 8.09293L21.5 8.80004ZM14.2 8.80004L14.2 9.80004L14.2 9.80004L14.2 8.80004ZM9.20001 12.8C9.20001 13.3523 9.64773 13.8 10.2 13.8C10.7523 13.8 11.2 13.3523 11.2 12.8L9.20001 12.8ZM18.2071 13.5071L22.2071 9.50714L20.7929 8.09293L16.7929 12.0929L18.2071 13.5071ZM22.2071 8.09293L18.2071 4.09293L16.7929 5.50714L20.7929 9.50714L22.2071 8.09293ZM21.5 7.80004L14.2 7.80004L14.2 9.80004L21.5 9.80004L21.5 7.80004ZM14.2 7.80004C11.4386 7.80005 9.20001 10.0386 9.20001 12.8L11.2 12.8C11.2 11.1432 12.5432 9.80005 14.2 9.80004L14.2 7.80004Z" fill="currentColor"/>
          </svg>
          Random
        </button>
        <button class="btn btn-share" id="share-btn">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 16H7C4.79086 16 3 14.2091 3 12V12C3 9.79086 4.79086 8 7 8H10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M16 12H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M14 16H17C19.2091 16 21 14.2091 21 12V12C21 9.79086 19.2091 8 17 8H14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          Share
        </button>
      </div>
    </div>

    <div class="author-info">
      Coded by <a href="#">Your Name Here</a> | Challenge by
      <a href="https://www.devchallenges.io?ref=challenge" target="_blank"
        >devChallenges.io</a
      >.
    </div>

    <script>
      const quotes = [
        {
          text: "Learn from yesterday, live for today, hope for tomorrow.",
          author: "George Bernard Shaw",
          tags: ["Famous Quotes", "Inspirational"]
        },
        {
          text: "The only way to do great work is to love what you do.",
          author: "Steve Jobs",
          tags: ["Motivational", "Work"]
        },
        {
          text: "Life is what happens to you while you're busy making other plans.",
          author: "John Lennon",
          tags: ["Life", "Philosophy"]
        },
        {
          text: "The future belongs to those who believe in the beauty of their dreams.",
          author: "Eleanor Roosevelt",
          tags: ["Dreams", "Future"]
        },
        {
          text: "It is during our darkest moments that we must focus to see the light.",
          author: "Aristotle",
          tags: ["Hope", "Wisdom"]
        },
        {
          text: "The only impossible journey is the one you never begin.",
          author: "Tony Robbins",
          tags: ["Journey", "Motivation"]
        },
        {
          text: "In the end, we will remember not the words of our enemies, but the silence of our friends.",
          author: "Martin Luther King Jr.",
          tags: ["Friendship", "Courage"]
        },
        {
          text: "Success is not final, failure is not fatal: it is the courage to continue that counts.",
          author: "Winston Churchill",
          tags: ["Success", "Courage"]
        },
        {
          text: "The way to get started is to quit talking and begin doing.",
          author: "Walt Disney",
          tags: ["Action", "Success"]
        },
        {
          text: "Don't let yesterday take up too much of today.",
          author: "Will Rogers",
          tags: ["Present", "Wisdom"]
        }
      ];

      let currentQuoteIndex = 0;

      function displayQuote(quote) {
        document.getElementById('quote-text').textContent = `"${quote.text}"`;
        document.getElementById('author-name').textContent = quote.author;

        const tagsContainer = document.querySelector('.author-tags');
        tagsContainer.innerHTML = '';
        quote.tags.forEach(tag => {
          const tagElement = document.createElement('span');
          tagElement.className = 'tag';
          tagElement.textContent = tag;
          tagsContainer.appendChild(tagElement);
        });
      }

      function getRandomQuote() {
        let randomIndex;
        do {
          randomIndex = Math.floor(Math.random() * quotes.length);
        } while (randomIndex === currentQuoteIndex && quotes.length > 1);

        currentQuoteIndex = randomIndex;
        return quotes[randomIndex];
      }

      function shareQuote() {
        const currentQuote = quotes[currentQuoteIndex];
        const shareText = `"${currentQuote.text}" - ${currentQuote.author}`;

        if (navigator.share) {
          navigator.share({
            title: 'Inspirational Quote',
            text: shareText,
            url: window.location.href
          });
        } else {
          // Fallback for browsers that don't support Web Share API
          navigator.clipboard.writeText(shareText).then(() => {
            alert('Quote copied to clipboard!');
          }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = shareText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('Quote copied to clipboard!');
          });
        }
      }

      // Event listeners
      document.getElementById('random-btn').addEventListener('click', () => {
        const newQuote = getRandomQuote();
        displayQuote(newQuote);
      });

      document.getElementById('share-btn').addEventListener('click', shareQuote);

      // Initialize with first quote
      displayQuote(quotes[currentQuoteIndex]);
    </script>
  </body>
</html>
